import GlobalStyles from '@/constants/GlobalStyles';
import React, {ReactNode} from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {useTheme} from 'styled-components/native';
import {ITheme} from '@/styles/themes';

export type Props = {
  title: string;
  children: ReactNode;
  isLast?: boolean;
};

const SettingsSection: React.FC<Props> = ({title, children, isLast = false}) => {
  const theme = useTheme() as ITheme;

  const styles = createStyles(theme);

  return (
    <View style={[styles.section, isLast && styles.lastSection]}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.sectionContent}>{children}</View>
    </View>
  );
};

const createStyles = (currentTheme: ITheme) =>
  StyleSheet.create({
    section: {
      width: '100%',
      marginBottom: 32,
    },
    lastSection: {
      marginBottom: 0,
    },
    sectionTitle: {
      fontSize: 18,
      fontFamily: GlobalStyles.fonts.sfPro,
      fontWeight: '600',
      color: currentTheme.colors.primary,
      marginBottom: 8,
      paddingHorizontal: 16,
    },
    sectionContent: {
      borderRadius: 8,
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      alignSelf: 'center',
      display: 'flex',
      width: '100%',
      shadowColor: currentTheme.colors.onSurface,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
    },
  });

export default SettingsSection;
