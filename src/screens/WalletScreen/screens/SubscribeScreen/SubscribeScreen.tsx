import {useIsFocused} from '@react-navigation/native';
import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Dimensions, SafeAreaView, StatusBar, View} from 'react-native';

import MButton from '@/components/MButton';
import SafeAreaInset from '@/components/SafeAreaInset';
import GlobalStyles from '@/constants/GlobalStyles';
import {Footer} from '@/styles/styled-components';
import {showInfoToast} from '@/utils/toast';
import LogoWhiteSvg from '../../../../assets/logo/AssetifyWhite.svg';
import FixedText from '../../../../components/FixedText/FixedText';
import {emailLoggerService} from '../../../../services/EmailLoggerService';
import {getScreenAlignment} from '../../../../utils/parsing';
import EmailInput from './components/Email_Input/Email_Input';
import SwitchWithLabel from './components/SwitchWithLabel/SwitchWithLabel';
import TopNavigation from './components/TopNavigation/TopNavigation';
import {styles} from './styles';
import {useEmailValidation} from './subscribeScreenUtils';

const SubscribeScreen: React.FC<any> = ({navigation}) => {
  const {t} = useTranslation();
  const viewFocused = useIsFocused();

  const [email, setEmail] = useState('');
  const [receiveUpdates, setReceiveUpdates] = useState(false);
  const [agreeTerms, setAgreeTerms] = useState(false);

  const isEmailValid = useEmailValidation(email);
  const isButtonDisabled = !isEmailValid || !receiveUpdates || !agreeTerms;

  const resetState = () => {
    setEmail('');
    setReceiveUpdates(false);
    setAgreeTerms(false);
  };

  const handleSubscribe = async () => {
    await emailLoggerService.logEmail(email);
    showInfoToast(
      'Thank you for subscribing! You will receive updates about the app and new features',
    );
  };

  const handleLeftIcon = () => {
    resetState();
    navigation.goBack();
  };

  useEffect(() => {
    if (viewFocused) resetState();
  }, [viewFocused]);

  const {height} = Dimensions.get('window');

  return (
    <>
      <StatusBar barStyle="light-content" />

      <View style={styles.mainContainer}>
        <SafeAreaInset type="top" />

        <SafeAreaView>
          <TopNavigation
            screenTitle={t('subscribe.title')}
            leftIcon
            leftIconAction={handleLeftIcon}
          />
        </SafeAreaView>
        <View style={styles.contentContainer}>
          <LogoWhiteSvg
            width={120}
            height={120}
            style={{marginTop: +getScreenAlignment(height, '25', '0')}}
          />
          <View style={styles.hooksContainer}>
            <FixedText style={[styles.hookText, {fontWeight: 'bold', marginBottom: -8}]}>
              {t('subscribe.intro')}
            </FixedText>
            <FixedText style={styles.hookText}>{t('subscribe.stay_tuned')}</FixedText>
          </View>
          <View style={styles.emailContainer}>
            <EmailInput email={email} setEmail={setEmail} />
          </View>
          <View style={styles.switchContainer}>
            <SwitchWithLabel
              disabled={false}
              label={t('subscribe.receive_updates')}
              isEnabled={receiveUpdates}
              handleToggleSwitch={() => setReceiveUpdates(!receiveUpdates)}
            />
            <SwitchWithLabel
              disabled={false}
              label={t('subscribe.agree_terms')}
              isEnabled={agreeTerms}
              handleToggleSwitch={() => setAgreeTerms(!agreeTerms)}
            />
          </View>

          <Footer style={styles.buttonContainer}>
            <MButton
              text={t('subscribe.title')}
              onPress={handleSubscribe}
              disabled={isButtonDisabled}
              variant="secondary"
              textStyle={{color: GlobalStyles.base.white}}
              containerStyle={{
                borderColor: GlobalStyles.base.white,
                borderRadius: 24,
                borderWidth: 1,
              }}
            />
          </Footer>
        </View>
      </View>
    </>
  );
};

export default SubscribeScreen;
