import {useIsFocused, useNavigation} from '@react-navigation/native';
import React, {memo, useEffect} from 'react';
import {TouchableOpacity} from 'react-native';
import {Cog6ToothIcon} from 'react-native-heroicons/outline';
import Animated, {
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withTiming,
} from 'react-native-reanimated';

import GlobalStyles from '@/constants/GlobalStyles';
import {useTheme} from 'styled-components/native';
import {ITheme} from '@/styles/themes';

// Constants for consistent animation values
const SETTINGS_ICON_ROTATION_DEGREES = 120;
const ANIMATION_DURATION = 250;
const PRESS_SCALE_FACTOR = 0.1;
const PRESS_OPACITY_FACTOR = 0.5;
const RESET_DELAY = 50;

const RotatingSettingsIcon = () => {
  const navigation = useNavigation<any>();
  const isScreenFocused = useIsFocused();
  const currentTheme = useTheme() as ITheme;

  const pressProgress = useSharedValue(0);

  // Reset animation when screen comes back into focus
  useEffect(() => {
    if (isScreenFocused) {
      pressProgress.value = withDelay(
        RESET_DELAY,
        withTiming(0, {duration: ANIMATION_DURATION}),
      );
    }
  }, [isScreenFocused, pressProgress]);

  // Handle navigation to Settings
  const handleNavigateToSettings = () => {
    navigation.navigate('LoanSettings');
  };

  // Handle press with animation
  const handlePress = () => {
    pressProgress.value = withTiming(1, {duration: ANIMATION_DURATION});

    // Navigate after animation starts
    setTimeout(() => {
      runOnJS(handleNavigateToSettings)();
    }, ANIMATION_DURATION / 2);
  };

  // Animated style with rotation, scale, and opacity
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      {rotate: `${pressProgress.value * SETTINGS_ICON_ROTATION_DEGREES}deg`},
      {scale: 1 - pressProgress.value * PRESS_SCALE_FACTOR},
    ],
    opacity: 1 - pressProgress.value * PRESS_OPACITY_FACTOR,
    justifyContent: 'center',
  }));

  return (
    <TouchableOpacity onPress={handlePress} hitSlop={20}>
      <Animated.View style={animatedStyle}>
        <Cog6ToothIcon size={29} color={currentTheme.colors.primary} />
      </Animated.View>
    </TouchableOpacity>
  );
};

export default memo(RotatingSettingsIcon);
