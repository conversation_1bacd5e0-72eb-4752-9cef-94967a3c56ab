import React, {memo, useReducer} from 'react';
import {StyleSheet, Text, View} from 'react-native';

import AssetifyLogo from '@/assets/logo/AssetifyWithText.svg';
import CarouselNormal from '@/components/CarouselNormal';
import MButton from '@/components/MButton';
import GlobalStyles from '@/constants/GlobalStyles';
import {navigateViaBottomTabs} from '@/navigation/utils/navigation';
import {Footer} from '@/styles/styled-components';
import theme, {getTheme} from '@/styles/themes';
import {QUOTES} from './utils';

const LoanOnboarding = () => {
  const currentTheme = getTheme('dark');

  const handleStartPress = () => {
    navigateViaBottomTabs('Loan', 'LoanRegister');
  };

  const renderQuote = ({item}: {item: any; index: number}) => (
    <View style={styles.quoteContainer}>
      <Text style={[styles.title, {color: currentTheme.colors.text}]}>{item.title}</Text>

      <Text style={[styles.description, {color: currentTheme.colors.text}]}>
        {item.description}
      </Text>
    </View>
  );

  return (
    <View style={[styles.root, {backgroundColor: currentTheme.colors.background}]}>
      <View style={styles.content}>
        <AssetifyLogo
          width={theme.isSmallDevice ? 140 : 150}
          height={theme.isSmallDevice ? 120 : 140}
        />

        <View style={styles.carouselContainer}>
          <CarouselNormal data={QUOTES} renderItem={renderQuote} />
        </View>
      </View>

      <Footer>
        <MButton text="Start Your Loan Application" onPress={handleStartPress} />
      </Footer>
    </View>
  );
};

export default memo(LoanOnboarding);

const styles = StyleSheet.create({
  root: {
    flex: 1,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    gap: 42,
    paddingTop: theme.spacing.xl,
  },
  carouselContainer: {
    flex: 1,
    marginVertical: theme.layout.pv.lg,
  },
  quoteContainer: {
    paddingHorizontal: theme.layout.ph.lg,
  },
  title: {
    fontFamily: GlobalStyles.fonts.poppins,
    fontSize: theme.isSmallDevice ? 20 : 22,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 18,
    letterSpacing: -0.5,
  },
  description: {
    fontFamily: GlobalStyles.fonts.poppins,
    fontSize: theme.isSmallDevice ? 16 : 18,
    textAlign: 'center',
    lineHeight: theme.isSmallDevice ? 24 : 26,
    letterSpacing: 0.2,
  },
});
